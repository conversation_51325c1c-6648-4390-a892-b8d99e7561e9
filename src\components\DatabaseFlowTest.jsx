import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { medicationDataService } from '../services/medicationDataService'
import { tokenSharingService } from '../services/tokenSharingService'
import { patientService } from '../services/database'
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Database,
  User,
  Key
} from 'lucide-react'

const DatabaseFlowTest = () => {
  const { user } = useAuth()
  const [tests, setTests] = useState([])
  const [running, setRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState('')

  const addTestResult = (testName, success, message, data = null) => {
    setTests(prev => [...prev, {
      id: Date.now(),
      testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    }])
    console.log(`🧪 Test: ${testName} - ${success ? '✅ PASS' : '❌ FAIL'}: ${message}`)
  }

  const clearTests = () => {
    setTests([])
  }

  const runAllTests = async () => {
    if (!user) {
      addTestResult('Auth Check', false, 'User not authenticated')
      return
    }

    setRunning(true)
    clearTests()

    try {
      // Test 1: Patient Profile Operations
      setCurrentTest('Testing patient profile operations...')
      await testPatientProfile()

      // Test 2: Medication Operations
      setCurrentTest('Testing medication operations...')
      await testMedicationOperations()

      // Test 3: Token Generation and Usage
      setCurrentTest('Testing token sharing...')
      await testTokenSharing()

      // Test 4: Database Query Validations
      setCurrentTest('Testing database queries...')
      await testDatabaseQueries()

      setCurrentTest('All tests completed!')
    } catch (error) {
      addTestResult('Test Suite', false, `Test suite failed: ${error.message}`)
    } finally {
      setRunning(false)
      setCurrentTest('')
    }
  }

  const testPatientProfile = async () => {
    try {
      // Test profile fetch with maybeSingle
      const profileResult = await medicationDataService.getPatientProfileByFirebaseUid(user.uid)
      if (profileResult.success) {
        addTestResult('Profile Fetch', true, 'Profile fetched successfully with maybeSingle', profileResult.profile)
      } else {
        addTestResult('Profile Fetch', false, `Profile fetch failed: ${profileResult.error?.message || 'Unknown error'}`)
      }

      // Test profile upsert
      const profileData = {
        full_name: `Test Patient ${Date.now()}`,
        email: user.email || '<EMAIL>',
        phone: '555-0123',
        date_of_birth: '1990-01-01',
        gender: 'Other',
        emergency_contact: 'Test Emergency Contact'
      }

      const upsertResult = await medicationDataService.upsertPatientProfile(profileData, user.uid)
      if (upsertResult.success) {
        addTestResult('Profile Upsert', true, 'Profile upserted successfully', upsertResult.profile)
      } else {
        addTestResult('Profile Upsert', false, `Profile upsert failed: ${upsertResult.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult('Patient Profile Test', false, `Exception: ${error.message}`)
    }
  }

  const testMedicationOperations = async () => {
    try {
      // Test medication fetch
      const fetchResult = await medicationDataService.getMedicationsByFirebaseUid(user.uid)
      if (fetchResult.success) {
        addTestResult('Medication Fetch', true, `Found ${fetchResult.medications.length} medications`, fetchResult.medications)
      } else {
        addTestResult('Medication Fetch', false, `Medication fetch failed: ${fetchResult.error?.message || 'Unknown error'}`)
      }

      // Test medication insert
      const medicationData = {
        medication_name: `Test Medication ${Date.now()}`,
        dosage: '10mg',
        frequency: 'Once daily',
        start_date: new Date().toISOString().split('T')[0],
        prescribing_doctor: 'Dr. Test',
        indication: 'Test condition'
      }

      const insertResult = await medicationDataService.addMedication(medicationData, user.uid)
      if (insertResult.success) {
        addTestResult('Medication Insert', true, 'Medication added successfully', insertResult.medication)
        
        // Test medication upsert (update)
        const updatedData = { ...medicationData, dosage: '20mg', id: insertResult.medication.id }
        const upsertResult = await medicationDataService.upsertMedication(updatedData, user.uid)
        if (upsertResult.success) {
          addTestResult('Medication Upsert', true, 'Medication updated successfully', upsertResult.medication)
        } else {
          addTestResult('Medication Upsert', false, `Medication upsert failed: ${upsertResult.error?.message || 'Unknown error'}`)
        }
      } else {
        addTestResult('Medication Insert', false, `Medication insert failed: ${insertResult.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult('Medication Operations Test', false, `Exception: ${error.message}`)
    }
  }

  const testTokenSharing = async () => {
    try {
      // Test token generation
      const generateResult = await tokenSharingService.generatePatientToken(user.uid, 1)
      if (generateResult.success) {
        addTestResult('Token Generation', true, `Generated token: ${generateResult.token}`, generateResult)
        
        // Test token usage (simulate doctor access)
        const useResult = await tokenSharingService.useAccessToken(generateResult.token, `doctor_${Date.now()}`)
        if (useResult.success) {
          addTestResult('Token Usage', true, 'Token used successfully', useResult)
        } else {
          addTestResult('Token Usage', false, `Token usage failed: ${useResult.error?.message || 'Unknown error'}`)
        }
      } else {
        addTestResult('Token Generation', false, `Token generation failed: ${generateResult.error?.message || 'Unknown error'}`)
      }

      // Test patient tokens fetch
      const tokensResult = await tokenSharingService.getPatientTokens(user.uid)
      if (tokensResult.success) {
        addTestResult('Patient Tokens Fetch', true, `Found ${tokensResult.tokens.length} tokens`, tokensResult.tokens)
      } else {
        addTestResult('Patient Tokens Fetch', false, `Tokens fetch failed: ${tokensResult.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult('Token Sharing Test', false, `Exception: ${error.message}`)
    }
  }

  const testDatabaseQueries = async () => {
    try {
      // Test patients query with proper error handling
      const patientsResult = await patientService.getPatients()
      addTestResult('Patients Query', true, `Found ${patientsResult?.length || 0} patients`, patientsResult)

      // Test medical history fetch
      const historyResult = await medicationDataService.getMedicalHistoryByFirebaseUid(user.uid)
      if (historyResult.success) {
        addTestResult('Medical History Fetch', true, `Found ${historyResult.history.length} history entries`, historyResult.history)
      } else {
        addTestResult('Medical History Fetch', false, `History fetch failed: ${historyResult.error?.message || 'Unknown error'}`)
      }

      // Test analysis results fetch
      const analysisResult = await medicationDataService.getAnalysisResultsByFirebaseUid(user.uid)
      if (analysisResult.success) {
        addTestResult('Analysis Results Fetch', true, `Found ${analysisResult.results.length} analysis results`, analysisResult.results)
      } else {
        addTestResult('Analysis Results Fetch', false, `Analysis fetch failed: ${analysisResult.error?.message || 'Unknown error'}`)
      }
    } catch (error) {
      addTestResult('Database Queries Test', false, `Exception: ${error.message}`)
    }
  }

  if (!user) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-yellow-600" />
          <span className="text-yellow-800">Please log in to run database flow tests</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Database className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Database Flow Test Suite</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={clearTests}
            className="px-3 py-2 text-gray-600 hover:text-gray-800"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          <button
            onClick={runAllTests}
            disabled={running}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <Play className="w-4 h-4 mr-2" />
            {running ? 'Running Tests...' : 'Run All Tests'}
          </button>
        </div>
      </div>

      {currentTest && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
            <span className="text-blue-800 text-sm">{currentTest}</span>
          </div>
        </div>
      )}

      <div className="space-y-2 max-h-96 overflow-y-auto">
        {tests.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Database className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>No tests run yet</p>
            <p className="text-sm">Click "Run All Tests" to start</p>
          </div>
        ) : (
          tests.map((test) => (
            <div
              key={test.id}
              className={`p-3 rounded-lg border ${
                test.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}
            >
              <div className="flex items-start space-x-2">
                {test.success ? (
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className={`font-medium ${
                      test.success ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {test.testName}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(test.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <p className={`text-sm ${
                    test.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {test.message}
                  </p>
                  {test.data && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-600 cursor-pointer">
                        View Data
                      </summary>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(test.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Test Coverage</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Patient dashboard data loading with maybeSingle()</li>
          <li>• Medication insert/upsert operations with proper logging</li>
          <li>• Token generation with UUID collision detection</li>
          <li>• Doctor token access flow validation</li>
          <li>• Firebase UID filtering on all queries</li>
          <li>• Error handling and debug logging</li>
        </ul>
      </div>
    </div>
  )
}

export default DatabaseFlowTest
