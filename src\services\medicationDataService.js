import { supabase } from '../lib/supabase'

// Enhanced medication data service with proper Firebase UID filtering
export const medicationDataService = {
  // Get all medications for a patient by Firebase UID
  async getMedicationsByFirebaseUid(firebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('medications')
        .select('*')
        .eq('patient_firebase_uid', firebaseUid)
        .eq('is_active', true)
        .order('start_date', { ascending: false })

      console.log('getMedicationsByFirebaseUid:', { data, error, status, firebaseUid })

      if (error) throw error
      return { success: true, medications: data || [] }
    } catch (error) {
      console.error('Error fetching medications by Firebase UID:', error)
      return { success: false, error }
    }
  },

  // Add a new medication for a patient
  async addMedication(medicationData, patientFirebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('medications')
        .insert([{
          ...medicationData,
          patient_firebase_uid: patientFirebaseUid,
          is_active: true,
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      console.log('addMedication:', { data, error, status, patientFirebaseUid })

      if (error) throw error
      return { success: true, medication: data }
    } catch (error) {
      console.error('Error adding medication:', error)
      return { success: false, error }
    }
  },

  // Update medication with upsert for conflict resolution
  async upsertMedication(medicationData, patientFirebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('medications')
        .upsert([{
          ...medicationData,
          patient_firebase_uid: patientFirebaseUid,
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      console.log('upsertMedication:', { data, error, status, patientFirebaseUid })

      if (error) throw error
      return { success: true, medication: data }
    } catch (error) {
      console.error('Error upserting medication:', error)
      return { success: false, error }
    }
  },

  // Get medical history for a patient by Firebase UID
  async getMedicalHistoryByFirebaseUid(firebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('medical_history')
        .select('*')
        .eq('patient_firebase_uid', firebaseUid)
        .order('diagnosis_date', { ascending: false })

      console.log('getMedicalHistoryByFirebaseUid:', { data, error, status, firebaseUid })

      if (error) throw error
      return { success: true, history: data || [] }
    } catch (error) {
      console.error('Error fetching medical history by Firebase UID:', error)
      return { success: false, error }
    }
  },

  // Add medical history entry
  async addMedicalHistory(historyData, patientFirebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('medical_history')
        .insert([{
          ...historyData,
          patient_firebase_uid: patientFirebaseUid,
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      console.log('addMedicalHistory:', { data, error, status, patientFirebaseUid })

      if (error) throw error
      return { success: true, history: data }
    } catch (error) {
      console.error('Error adding medical history:', error)
      return { success: false, error }
    }
  },

  // Get patient profile by Firebase UID with maybeSingle
  async getPatientProfileByFirebaseUid(firebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('profiles')
        .select('*')
        .eq('firebase_uid', firebaseUid)
        .eq('role', 'patient')
        .maybeSingle()

      console.log('getPatientProfileByFirebaseUid:', { data, error, status, firebaseUid })

      if (error) throw error
      return { success: true, profile: data }
    } catch (error) {
      console.error('Error fetching patient profile by Firebase UID:', error)
      return { success: false, error }
    }
  },

  // Upsert patient profile (insert or update if exists)
  async upsertPatientProfile(profileData, firebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('profiles')
        .upsert([{
          ...profileData,
          firebase_uid: firebaseUid,
          role: 'patient',
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      console.log('upsertPatientProfile:', { data, error, status, firebaseUid })

      if (error) throw error
      return { success: true, profile: data }
    } catch (error) {
      console.error('Error upserting patient profile:', error)
      return { success: false, error }
    }
  },

  // Get analysis results for a patient by Firebase UID
  async getAnalysisResultsByFirebaseUid(firebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('analysis_results')
        .select('*')
        .eq('patient_firebase_uid', firebaseUid)
        .order('session_date', { ascending: false })
        .limit(20)

      console.log('getAnalysisResultsByFirebaseUid:', { data, error, status, firebaseUid })

      if (error) throw error
      return { success: true, results: data || [] }
    } catch (error) {
      console.error('Error fetching analysis results by Firebase UID:', error)
      return { success: false, error }
    }
  },

  // Add analysis result
  async addAnalysisResult(resultData, patientFirebaseUid) {
    try {
      const { data, error, status } = await supabase
        .from('analysis_results')
        .insert([{
          ...resultData,
          patient_firebase_uid: patientFirebaseUid,
          session_date: resultData.session_date || new Date().toISOString(),
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      console.log('addAnalysisResult:', { data, error, status, patientFirebaseUid })

      if (error) throw error
      return { success: true, result: data }
    } catch (error) {
      console.error('Error adding analysis result:', error)
      return { success: false, error }
    }
  }
}

export default medicationDataService
