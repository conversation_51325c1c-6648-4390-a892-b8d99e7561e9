@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  #root {
    @apply min-h-screen;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2;
  }
  
  .btn-primary {
    @apply btn bg-medical-primary text-white hover:bg-medical-primary/90;
  }
  
  .btn-secondary {
    @apply btn bg-medical-secondary text-white hover:bg-medical-secondary/90;
  }
  
  .btn-success {
    @apply btn bg-medical-success text-white hover:bg-medical-success/90;
  }
  
  .btn-warning {
    @apply btn bg-medical-warning text-white hover:bg-medical-warning/90;
  }
  
  .btn-danger {
    @apply btn bg-medical-danger text-white hover:bg-medical-danger/90;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .form-input {
    @apply block w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 focus:border-medical-primary focus:outline-none focus:ring-1 focus:ring-medical-primary;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .sidebar-nav-item-active {
    @apply bg-medical-primary text-white;
  }
  
  .sidebar-nav-item-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
}
