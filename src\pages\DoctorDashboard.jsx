import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import DoctorTokenAccess from '../components/DoctorTokenAccess'
import {
  Users,
  FileText,
  Download,
  Search,
  AlertTriangle,
  LogOut,
  Calendar,
  Activity,
  Stethoscope,
  Pill,
  Eye,
  Key,
  X,
  RefreshCw,
  Plus,
  Edit
} from 'lucide-react'
import { patientService, diagnosisService, medicationService } from '../services/database'
import { medicationDataService } from '../services/medicationDataService'
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const DoctorDashboard = () => {
  const { user, signOut } = useAuth()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [patients, setPatients] = useState([])
  const [connectedPatients, setConnectedPatients] = useState([])
  const [selectedPatient, setSelectedPatient] = useState(null)
  const [patientSessions, setPatientSessions] = useState([])
  const [sharedPatientId, setSharedPatientId] = useState('')
  const [showShareModal, setShowShareModal] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Load connected patients via token sharing with enhanced data
  const loadConnectedPatients = async () => {
    if (!user?.uid) {
      console.log('No user UID available for loading connected patients')
      return
    }

    try {
      console.log('🔄 Loading connected patients for doctor:', user.uid)

      const { data, error } = await supabase
        .from('patient_doctor_connections')
        .select(`
          *,
          patient:profiles!patient_doctor_connections_patient_firebase_uid_fkey(
            id,
            firebase_uid,
            full_name,
            email,
            date_of_birth,
            gender,
            phone,
            address
          )
        `)
        .eq('doctor_firebase_uid', user.uid)
        .eq('is_active', true)
        .gte('token_expires_at', new Date().toISOString())
        .order('last_accessed_at', { ascending: false, nullsFirst: false })

      console.log('📊 Connected patients query result:', { data, error, doctorUid: user.uid })

      if (error) {
        console.error('❌ Error loading connected patients:', error)
      } else {
        console.log('✅ Connected patients loaded successfully:', {
          count: data?.length || 0,
          patients: data?.map(d => d.patient?.full_name || 'Unknown') || []
        })

        setConnectedPatients(data || [])

        // Also load medical summaries for each patient
        if (data && data.length > 0) {
          console.log('📋 Loading medical summaries for', data.length, 'patients')
          for (const connection of data) {
            await loadPatientMedicalSummary(connection.patient_firebase_uid)
          }
        } else {
          console.log('ℹ️ No connected patients found')
        }
      }
    } catch (err) {
      console.error('💥 Exception loading connected patients:', err)
    }
  }

  // Load patient medical summary using the updated service
  const loadPatientMedicalSummary = async (patientFirebaseUid) => {
    try {
      console.log('Loading medical summary for patient:', patientFirebaseUid)

      // Use the updated medicationDataService for consistent data fetching
      const [historyResult, medicationsResult] = await Promise.all([
        medicationDataService.getMedicalHistoryByFirebaseUid(patientFirebaseUid),
        medicationDataService.getMedicationsByFirebaseUid(patientFirebaseUid)
      ])

      console.log('Medical summary results:', { historyResult, medicationsResult })

      if (historyResult.success && medicationsResult.success) {
        // Store medical summaries for display with deduplication
        setConnectedPatients(prev => prev.map(connection => {
          if (connection.patient_firebase_uid === patientFirebaseUid) {
            return {
              ...connection,
              medicalHistory: historyResult.history?.slice(0, 5) || [], // Limit to 5 most recent
              activeMedications: medicationsResult.medications?.slice(0, 5) || [], // Limit to 5 most recent
              lastDataUpdate: new Date().toISOString()
            }
          }
          return connection
        }))
      } else {
        console.error('Error loading medical summary:', {
          historyError: historyResult.error,
          medicationsError: medicationsResult.error
        })
      }
    } catch (err) {
      console.error('Exception loading patient medical summary:', err)
    }
  }

  const refreshData = async () => {
    setRefreshing(true)
    try {
      await loadConnectedPatients()
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    const fetchDoctorData = async () => {
      try {
        setLoading(true)

        // Fetch all patients treated by this doctor
        const patientsData = await patientService.getPatients()
        setPatients(patientsData)

        // Load connected patients via token sharing
        await loadConnectedPatients()

      } catch (error) {
        console.error('Error fetching doctor data:', error)
      } finally {
        setLoading(false)
      }
    }

    if (user) {
      fetchDoctorData()
    }

    // Listen for patient connection updates and data changes
    const handlePatientConnectionUpdate = async (event) => {
      console.log('Patient connection updated:', event.detail)
      console.log('Triggering connected patients refresh from event...')
      await loadConnectedPatients()

      // Force a UI refresh
      setRefreshing(true)
      setTimeout(() => setRefreshing(false), 500)
    }

    const handlePatientDataUpdate = (event) => {
      console.log('Patient data updated:', event.detail)
      // Refresh specific patient's medical summary
      if (event.detail.patientFirebaseUid) {
        loadPatientMedicalSummary(event.detail.patientFirebaseUid)
      }
    }

    // Listen for various patient-related events
    window.addEventListener('doctorPatientConnectionUpdated', handlePatientConnectionUpdate)
    window.addEventListener('patientMedicationUpdated', handlePatientDataUpdate)
    window.addEventListener('patientMedicalHistoryUpdated', handlePatientDataUpdate)

    // Set up periodic refresh for real-time data synchronization
    const refreshInterval = setInterval(() => {
      if (connectedPatients.length > 0) {
        console.log('Periodic refresh of connected patients data')
        loadConnectedPatients()
      }
    }, 30000) // Refresh every 30 seconds

    return () => {
      window.removeEventListener('doctorPatientConnectionUpdated', handlePatientConnectionUpdate)
      window.removeEventListener('patientMedicationUpdated', handlePatientDataUpdate)
      window.removeEventListener('patientMedicalHistoryUpdated', handlePatientDataUpdate)
      clearInterval(refreshInterval)
    }
  }, [user, connectedPatients.length])

  const handlePatientSelect = async (patient) => {
    try {
      setSelectedPatient(patient)
      
      // Fetch sessions for selected patient
      const [diagnosisSessions, medicationSessions] = await Promise.all([
        diagnosisService.getDiagnosisSessionsByPatient(patient.id),
        medicationService.getMedicationSessionsByPatient(patient.id)
      ])
      
      const allSessions = [
        ...diagnosisSessions.map(session => ({ ...session, type: 'diagnosis' })),
        ...medicationSessions.map(session => ({ ...session, type: 'medication' }))
      ].sort((a, b) => new Date(b.session_date) - new Date(a.session_date))
      
      setPatientSessions(allSessions)
    } catch (error) {
      console.error('Error fetching patient sessions:', error)
    }
  }

  const handlePatientAccess = async (patient) => {
    // When a doctor successfully accesses a patient via token
    console.log('Doctor accessed patient:', patient)
    setShowShareModal(false)

    // Immediately refresh the connected patients list to show the new connection
    console.log('Refreshing connected patients after token access...')
    await loadConnectedPatients()

    // Also trigger a manual refresh to ensure UI updates
    setRefreshing(true)
    setTimeout(() => setRefreshing(false), 1000)
  }

  const handleConnectionUpdate = async (updateData) => {
    console.log('Connection update received:', updateData)

    // Immediately refresh connected patients
    await loadConnectedPatients()

    // Show visual feedback
    setRefreshing(true)
    setTimeout(() => setRefreshing(false), 500)
  }

  const downloadPatientReport = () => {
    if (!selectedPatient) return
    
    const doc = new jsPDF()
    
    // Header
    doc.setFontSize(20)
    doc.text('Patient Medical Report', 20, 30)
    
    // Patient Info
    doc.setFontSize(12)
    doc.text(`Patient: ${selectedPatient.first_name} ${selectedPatient.last_name}`, 20, 50)
    doc.text(`Medical Record: ${selectedPatient.medical_record_number || 'N/A'}`, 20, 60)
    doc.text(`Age: ${selectedPatient.age || 'N/A'}`, 20, 70)
    doc.text(`Gender: ${selectedPatient.gender || 'N/A'}`, 20, 80)
    doc.text(`Doctor: ${user?.email}`, 20, 90)
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 100)
    
    let yPosition = 120
    
    // Sessions History
    doc.setFontSize(14)
    doc.text('Medical Sessions History', 20, yPosition)
    yPosition += 10
    
    if (patientSessions.length > 0) {
      const sessionData = patientSessions.map(session => [
        new Date(session.session_date).toLocaleDateString(),
        session.type === 'diagnosis' ? 'Diagnosis' : 'Medication',
        session.type === 'diagnosis' 
          ? (session.final_diagnosis || 'Analysis') 
          : (session.medications_checked || 'Medication Check'),
        session.risk_level || session.severity_level || 'Low',
        session.status || 'Completed'
      ])
      
      doc.autoTable({
        startY: yPosition,
        head: [['Date', 'Type', 'Details', 'Risk/Severity', 'Status']],
        body: sessionData,
        margin: { left: 20 }
      })
    } else {
      doc.text('No session history available.', 20, yPosition)
    }
    
    doc.save(`patient-report-${selectedPatient.first_name}-${selectedPatient.last_name}.pdf`)
  }

  const handleSignOut = async () => {
    try {
      await signOut()
      navigate('/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const filteredPatients = patients.filter(patient =>
    `${patient.first_name} ${patient.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (patient.medical_record_number || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Activity className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading doctor dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Stethoscope className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">
                Doctor Dashboard
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowShareModal(true)}
                className="inline-flex items-center px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100"
              >
                <Key className="h-4 w-4 mr-2" />
                Access Shared Patient
              </button>
              
              <span className="text-sm text-gray-700">
                Dr. {user?.email}
              </span>
              <button
                onClick={handleSignOut}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Connected Patients via Token Sharing */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Connected Patients</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">{connectedPatients.length} connected</span>
                  <button
                    onClick={refreshData}
                    disabled={refreshing}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {connectedPatients.length === 0 ? (
                <div className="px-6 py-8 text-center text-gray-500">
                  <p className="text-sm">No connected patients yet</p>
                  <p className="text-xs mt-1">Use "Access Patient Data" to connect via token</p>
                </div>
              ) : (
                connectedPatients.map((connection) => (
                  <div
                    key={connection.id}
                    onClick={() => setSelectedPatient(connection.patient)}
                    className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${
                      selectedPatient?.firebase_uid === connection.patient_firebase_uid ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {connection.patient?.full_name || 'Unknown Patient'}
                        </p>
                        <p className="text-sm text-gray-600">
                          {connection.patient?.email}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs text-gray-500">
                            Accessed: {connection.access_count || 0} times
                        </span>
                        {connection.last_accessed_at && (
                          <span className="text-xs text-gray-500">
                            • Last: {new Date(connection.last_accessed_at).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {connection.medicalHistory && connection.activeMedications && (
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {connection.medicalHistory.length} conditions
                          </span>
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                            {connection.activeMedications.length} medications
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-400">
                      <Key className="w-3 h-3" />
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>

          {/* Regular Patients List */}
          <div className="lg:col-span-1 bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">My Patients</h3>
                <span className="text-sm text-gray-500">{patients.length} total</span>
              </div>
              
              <div className="mt-4 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search patients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {filteredPatients.map((patient) => (
                <div
                  key={patient.id}
                  onClick={() => handlePatientSelect(patient)}
                  className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${
                    selectedPatient?.id === patient.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                  }`}
                >
                  <p className="text-sm font-medium text-gray-900">
                    {patient.first_name} {patient.last_name}
                  </p>
                  <p className="text-sm text-gray-600">
                    MRN: {patient.medical_record_number || 'N/A'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {patient.age ? `Age: ${patient.age}` : ''} {patient.gender ? `• ${patient.gender}` : ''}
                  </p>
                </div>
              ))}
              
              {filteredPatients.length === 0 && (
                <div className="px-6 py-4 text-center text-gray-500">
                  No patients found
                </div>
              )}
            </div>
          </div>

          {/* Patient Details */}
          <div className="lg:col-span-2 space-y-6">
            {selectedPatient ? (
              <>
                {/* Patient Info */}
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-medium text-gray-900">
                        {selectedPatient.first_name} {selectedPatient.last_name}
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Medical Record: {selectedPatient.medical_record_number || 'N/A'}
                      </p>
                      <div className="mt-2 space-y-1">
                        <p className="text-sm text-gray-600">Age: {selectedPatient.age || 'N/A'}</p>
                        <p className="text-sm text-gray-600">Gender: {selectedPatient.gender || 'N/A'}</p>
                        <p className="text-sm text-gray-600">Allergies: {selectedPatient.allergies || 'None listed'}</p>
                      </div>
                    </div>
                    <button
                      onClick={downloadPatientReport}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Report
                    </button>
                  </div>
                </div>

                {/* Session History */}
                <div className="bg-white rounded-lg shadow">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">Medical Session History</h3>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {patientSessions.length > 0 ? (
                      patientSessions.map((session) => (
                        <div key={`${session.type}-${session.id}`} className="px-6 py-4 hover:bg-gray-50">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="flex items-center space-x-2">
                                {session.type === 'diagnosis' ? (
                                  <FileText className="h-4 w-4 text-blue-500" />
                                ) : (
                                  <Pill className="h-4 w-4 text-green-500" />
                                )}
                                <p className="text-sm font-medium text-gray-900">
                                  {session.type === 'diagnosis' ? 'Diagnosis Analysis' : 'Medication Check'}
                                </p>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">
                                {session.type === 'diagnosis' 
                                  ? (session.final_diagnosis || 'Analysis performed')
                                  : (session.medications_checked || 'Medications reviewed')
                                }
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {new Date(session.session_date).toLocaleDateString()} • 
                                Status: {session.status || 'Completed'}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                (session.risk_level === 'high' || session.severity_level === 'high') ? 'bg-red-100 text-red-800' :
                                (session.risk_level === 'medium' || session.severity_level === 'medium') ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {session.risk_level || session.severity_level || 'Low'} {session.type === 'diagnosis' ? 'Risk' : 'Severity'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="px-6 py-4 text-center text-gray-500">
                        No medical sessions found for this patient
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-lg shadow p-6 text-center">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Patient</h3>
                <p className="text-gray-600">Choose a patient from the list to view their medical history and session details.</p>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Patient Token Access Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-4 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Access Patient Data</h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <DoctorTokenAccess
              onPatientAccess={handlePatientAccess}
              onConnectionUpdate={handleConnectionUpdate}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default DoctorDashboard
