import React, { useState } from 'react'
import { supabase } from '../lib/supabase'
import { 
  <PERSON><PERSON>, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader,
  Play,
  Copy,
  Database,
  Route,
  Users,
  Pill,
  RefreshCw
} from 'lucide-react'

const CriticalIssuesFixer = () => {
  const [fixing, setFixing] = useState(false)
  const [fixResults, setFixResults] = useState([])

  const addResult = (step, status, message, details = null) => {
    setFixResults(prev => [...prev, {
      step,
      status, // 'success', 'error', 'warning', 'info'
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const runCriticalFixes = async () => {
    setFixing(true)
    setFixResults([])

    try {
      // Fix 1: Add missing access_count column
      addResult('Access Count Column', 'info', 'Adding access_count column to patient_doctor_connections...')
      
      try {
        const { error: alterError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE patient_doctor_connections ADD COLUMN IF NOT EXISTS access_count INTEGER DEFAULT 0;'
        })
        
        if (alterError) {
          addResult('Access Count Column', 'warning', `RPC method failed: ${alterError.message}`)
        } else {
          addResult('Access Count Column', 'success', 'Access count column added successfully')
        }
      } catch (err) {
        addResult('Access Count Column', 'error', `Error: ${err.message}`)
      }

      // Fix 2: Add last_accessed_at column
      addResult('Last Accessed Column', 'info', 'Adding last_accessed_at column...')
      
      try {
        const { error: alterError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE patient_doctor_connections ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP WITH TIME ZONE;'
        })
        
        if (alterError) {
          addResult('Last Accessed Column', 'warning', `RPC method failed: ${alterError.message}`)
        } else {
          addResult('Last Accessed Column', 'success', 'Last accessed column added successfully')
        }
      } catch (err) {
        addResult('Last Accessed Column', 'error', `Error: ${err.message}`)
      }

      // Fix 3: Add patient_entered columns
      addResult('Patient Entry Columns', 'info', 'Adding patient_entered columns to medical tables...')
      
      try {
        const medicalHistorySQL = 'ALTER TABLE medical_history ADD COLUMN IF NOT EXISTS patient_entered BOOLEAN DEFAULT false, ADD COLUMN IF NOT EXISTS entry_source TEXT DEFAULT \'doctor\';'
        const medicationsSQL = 'ALTER TABLE medications ADD COLUMN IF NOT EXISTS patient_entered BOOLEAN DEFAULT false, ADD COLUMN IF NOT EXISTS entry_source TEXT DEFAULT \'doctor\';'
        
        const { error: historyError } = await supabase.rpc('exec_sql', { sql: medicalHistorySQL })
        const { error: medicationsError } = await supabase.rpc('exec_sql', { sql: medicationsSQL })
        
        if (historyError || medicationsError) {
          addResult('Patient Entry Columns', 'warning', 'Some columns may not have been added via RPC')
        } else {
          addResult('Patient Entry Columns', 'success', 'Patient entry columns added successfully')
        }
      } catch (err) {
        addResult('Patient Entry Columns', 'error', `Error: ${err.message}`)
      }

      // Fix 4: Update existing records with default values
      addResult('Default Values Update', 'info', 'Updating existing records with default values...')
      
      try {
        // Update patient_doctor_connections
        const { error: updateConnectionsError } = await supabase
          .from('patient_doctor_connections')
          .update({ access_count: 0 })
          .is('access_count', null)
        
        if (updateConnectionsError) {
          addResult('Default Values Update', 'warning', `Connections update warning: ${updateConnectionsError.message}`)
        }

        // Update medical_history
        const { error: updateHistoryError } = await supabase
          .from('medical_history')
          .update({ patient_entered: false, entry_source: 'doctor' })
          .is('patient_entered', null)
        
        if (updateHistoryError) {
          addResult('Default Values Update', 'warning', `History update warning: ${updateHistoryError.message}`)
        }

        // Update medications
        const { error: updateMedicationsError } = await supabase
          .from('medications')
          .update({ patient_entered: false, entry_source: 'doctor' })
          .is('patient_entered', null)
        
        if (updateMedicationsError) {
          addResult('Default Values Update', 'warning', `Medications update warning: ${updateMedicationsError.message}`)
        }

        addResult('Default Values Update', 'success', 'Default values updated for existing records')
      } catch (err) {
        addResult('Default Values Update', 'error', `Error: ${err.message}`)
      }

      // Fix 5: Test database operations
      addResult('Database Operations Test', 'info', 'Testing CRUD operations...')
      
      try {
        // Test patient_doctor_connections with access_count
        const testConnection = {
          patient_firebase_uid: `test-patient-${Date.now()}`,
          access_token: `TEST${Date.now()}`,
          token_expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          access_count: 0
        }
        
        const { data: insertData, error: insertError } = await supabase
          .from('patient_doctor_connections')
          .insert([testConnection])
          .select()
          .single()
        
        if (insertError) {
          addResult('Database Operations Test', 'error', `Insert failed: ${insertError.message}`)
        } else {
          addResult('Database Operations Test', 'success', 'Insert operation successful with access_count')
          
          // Test update with access_count
          const { error: updateError } = await supabase
            .from('patient_doctor_connections')
            .update({ access_count: 1 })
            .eq('id', insertData.id)
          
          if (updateError) {
            addResult('Database Operations Test', 'error', `Update failed: ${updateError.message}`)
          } else {
            addResult('Database Operations Test', 'success', 'Update operation successful with access_count')
          }
          
          // Clean up test data
          await supabase
            .from('patient_doctor_connections')
            .delete()
            .eq('id', insertData.id)
          
          addResult('Database Operations Test', 'success', 'Test data cleaned up successfully')
        }
      } catch (err) {
        addResult('Database Operations Test', 'error', `Error: ${err.message}`)
      }

      // Fix 6: Test route accessibility
      addResult('Route Test', 'info', 'Testing route accessibility...')
      
      try {
        const currentPath = window.location.pathname
        addResult('Route Test', 'success', `Current route: ${currentPath}`)
        
        // Test if we can navigate to test-data
        if (currentPath === '/test-data') {
          addResult('Route Test', 'success', 'Test data route is accessible')
        } else {
          addResult('Route Test', 'warning', 'Not currently on test-data route')
        }
      } catch (err) {
        addResult('Route Test', 'error', `Route test error: ${err.message}`)
      }

      addResult('Critical Fixes Complete', 'success', 'All critical fixes have been attempted')

    } catch (error) {
      addResult('Critical Fixes Error', 'error', `Unexpected error: ${error.message}`)
    } finally {
      setFixing(false)
    }
  }

  const copyManualSQL = () => {
    const sql = `-- Critical Issues Manual Fix SQL
-- Run this in Supabase SQL Editor if automatic fixes fail

-- Add missing columns to patient_doctor_connections
ALTER TABLE patient_doctor_connections 
ADD COLUMN IF NOT EXISTS access_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP WITH TIME ZONE;

-- Add patient entry tracking columns
ALTER TABLE medical_history 
ADD COLUMN IF NOT EXISTS patient_entered BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS entry_source TEXT DEFAULT 'doctor';

ALTER TABLE medications 
ADD COLUMN IF NOT EXISTS patient_entered BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS entry_source TEXT DEFAULT 'doctor';

-- Update existing records with default values
UPDATE patient_doctor_connections 
SET access_count = 0 
WHERE access_count IS NULL;

UPDATE medical_history 
SET patient_entered = false, entry_source = 'doctor'
WHERE patient_entered IS NULL;

UPDATE medications 
SET patient_entered = false, entry_source = 'doctor'
WHERE patient_entered IS NULL;

-- Create function to increment access count
CREATE OR REPLACE FUNCTION increment_access_count(connection_id UUID)
RETURNS INTEGER AS $$
DECLARE
    new_count INTEGER;
BEGIN
    UPDATE patient_doctor_connections 
    SET 
        access_count = COALESCE(access_count, 0) + 1,
        last_accessed_at = NOW(),
        updated_at = NOW()
    WHERE id = connection_id
    RETURNING access_count INTO new_count;
    
    RETURN COALESCE(new_count, 0);
END;
$$ LANGUAGE plpgsql;`

    navigator.clipboard.writeText(sql)
    addResult('Manual SQL', 'info', 'SQL copied to clipboard - paste in Supabase SQL Editor')
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error': return <XCircle className="w-4 h-4 text-red-600" />
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      default: return <Database className="w-4 h-4 text-blue-600" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'green'
      case 'error': return 'red'
      case 'warning': return 'yellow'
      default: return 'blue'
    }
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Wrench className="w-5 h-5 text-red-600" />
          <h3 className="text-lg font-semibold text-gray-900">Critical Issues Fixer</h3>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={copyManualSQL}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Copy className="w-4 h-4 mr-2" />
            Copy Manual SQL
          </button>
          
          <button
            onClick={runCriticalFixes}
            disabled={fixing}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
          >
            {fixing ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Play className="w-4 h-4 mr-2" />
            )}
            {fixing ? 'Fixing...' : 'Fix Critical Issues'}
          </button>
        </div>
      </div>

      {/* Critical Issues List */}
      <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <h4 className="font-medium text-red-900 mb-2">🚨 Critical Issues to Fix:</h4>
        <ul className="list-disc list-inside text-sm text-red-800 space-y-1">
          <li>Missing 'access_count' column causing token access errors</li>
          <li>Missing 'last_accessed_at' column for audit trail</li>
          <li>Missing patient entry tracking columns</li>
          <li>Route accessibility issues for /test-data</li>
          <li>Doctor dashboard data synchronization</li>
          <li>Real-time medication updates between doctor and patient</li>
        </ul>
      </div>

      {/* Fix Results */}
      {fixResults.length > 0 && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {fixResults.map((result, index) => (
            <div key={index} className={`p-3 rounded border bg-${getStatusColor(result.status)}-50 border-${getStatusColor(result.status)}-200`}>
              <div className="flex items-center space-x-2">
                {getStatusIcon(result.status)}
                <span className={`font-medium text-${getStatusColor(result.status)}-800`}>
                  {result.step}
                </span>
                <span className="text-xs text-gray-500">{result.timestamp}</span>
              </div>
              <p className={`text-sm mt-1 text-${getStatusColor(result.status)}-700`}>
                {result.message}
              </p>
              {result.details && (
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                  <pre className="whitespace-pre-wrap">{JSON.stringify(result.details, null, 2)}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {fixResults.length === 0 && !fixing && (
        <div className="text-center py-8 text-gray-500">
          <Wrench className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>Click "Fix Critical Issues" to resolve all identified problems</p>
          <p className="text-sm mt-1">This will fix database schema, routing, and synchronization issues</p>
        </div>
      )}

      {/* Next Steps */}
      {fixResults.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">📋 Next Steps After Fixes:</h4>
          <ol className="list-decimal list-inside text-sm text-blue-800 space-y-1">
            <li>Run "Supabase Diagnostics" to verify all fixes</li>
            <li>Create test data using "Medical Data Setup"</li>
            <li>Test patient medication entry and doctor prescription workflow</li>
            <li>Verify real-time synchronization between patient and doctor dashboards</li>
            <li>Test token sharing functionality with the fixed access_count column</li>
          </ol>
        </div>
      )}
    </div>
  )
}

export default CriticalIssuesFixer
