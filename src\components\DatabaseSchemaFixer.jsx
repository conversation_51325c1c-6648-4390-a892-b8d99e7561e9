import React, { useState } from 'react'
import { supabase } from '../lib/supabase'
import { 
  Database, 
  Wrench, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Loader,
  Play,
  Copy
} from 'lucide-react'

const DatabaseSchemaFixer = () => {
  const [fixing, setFixing] = useState(false)
  const [fixResults, setFixResults] = useState([])
  const [showSQL, setShowSQL] = useState(false)

  const addResult = (step, success, message, details = null) => {
    setFixResults(prev => [...prev, {
      step,
      success,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const runSchemaFixes = async () => {
    setFixing(true)
    setFixResults([])

    try {
      // Fix 1: Add emergency_contact column
      addResult('Emergency Contact Column', 'info', 'Adding emergency_contact column to profiles table...')
      
      try {
        const { error: alterError } = await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE profiles ADD COLUMN IF NOT EXISTS emergency_contact JSONB;'
        })
        
        if (alterError) {
          // Try alternative method
          const { error: directError } = await supabase
            .from('profiles')
            .select('emergency_contact')
            .limit(1)
          
          if (directError && directError.message.includes('column "emergency_contact" does not exist')) {
            addResult('Emergency Contact Column', 'error', 'Column does not exist and cannot be added automatically')
          } else {
            addResult('Emergency Contact Column', 'success', 'Column already exists or was added successfully')
          }
        } else {
          addResult('Emergency Contact Column', 'success', 'Emergency contact column added successfully')
        }
      } catch (err) {
        addResult('Emergency Contact Column', 'error', `Error: ${err.message}`)
      }

      // Fix 2: Create patients view
      addResult('Patients View', 'info', 'Creating patients view for compatibility...')
      
      try {
        const createViewSQL = `
          CREATE OR REPLACE VIEW patients AS
          SELECT 
            id, firebase_uid, email, full_name, phone, date_of_birth, gender, 
            address, emergency_contact, profile_picture, bio, is_active, 
            created_at, updated_at
          FROM profiles 
          WHERE role = 'patient';
        `
        
        const { error: viewError } = await supabase.rpc('exec_sql', { sql: createViewSQL })
        
        if (viewError) {
          addResult('Patients View', 'error', `View creation failed: ${viewError.message}`)
        } else {
          addResult('Patients View', 'success', 'Patients view created successfully')
        }
      } catch (err) {
        addResult('Patients View', 'error', `Error: ${err.message}`)
      }

      // Fix 3: Test foreign key constraints
      addResult('Foreign Key Test', 'info', 'Testing foreign key constraints...')
      
      try {
        // Test if we can query with foreign key relationships
        const { data, error } = await supabase
          .from('patient_doctor_connections')
          .select(`
            *,
            patient:profiles!patient_doctor_connections_patient_firebase_uid_fkey(*),
            doctor:profiles!patient_doctor_connections_doctor_firebase_uid_fkey(*)
          `)
          .limit(1)
        
        if (error) {
          addResult('Foreign Key Test', 'warning', `Foreign key relationships may need fixing: ${error.message}`)
        } else {
          addResult('Foreign Key Test', 'success', 'Foreign key relationships are working correctly')
        }
      } catch (err) {
        addResult('Foreign Key Test', 'error', `Error: ${err.message}`)
      }

      // Fix 4: Update sample data with emergency contacts
      addResult('Sample Data Update', 'info', 'Updating existing profiles with emergency contact data...')
      
      try {
        const { data: profiles, error: selectError } = await supabase
          .from('profiles')
          .select('id, firebase_uid, full_name')
          .eq('role', 'patient')
          .is('emergency_contact', null)
          .limit(5)
        
        if (selectError) {
          addResult('Sample Data Update', 'error', `Error selecting profiles: ${selectError.message}`)
        } else if (profiles && profiles.length > 0) {
          for (const profile of profiles) {
            const emergencyContact = {
              name: `Emergency Contact for ${profile.full_name}`,
              relationship: 'Family',
              phone: '******-0000',
              email: '<EMAIL>'
            }
            
            const { error: updateError } = await supabase
              .from('profiles')
              .update({ emergency_contact: emergencyContact })
              .eq('id', profile.id)
            
            if (updateError) {
              addResult('Sample Data Update', 'warning', `Failed to update ${profile.full_name}: ${updateError.message}`)
            }
          }
          
          addResult('Sample Data Update', 'success', `Updated ${profiles.length} profiles with emergency contact data`)
        } else {
          addResult('Sample Data Update', 'success', 'All profiles already have emergency contact data')
        }
      } catch (err) {
        addResult('Sample Data Update', 'error', `Error: ${err.message}`)
      }

      // Fix 5: Test database operations
      addResult('Database Operations Test', 'info', 'Testing CRUD operations...')
      
      try {
        // Test insert
        const testProfile = {
          firebase_uid: `test-fix-${Date.now()}`,
          email: '<EMAIL>',
          full_name: 'Schema Fix Test',
          role: 'patient',
          emergency_contact: {
            name: 'Test Emergency Contact',
            phone: '******-TEST'
          }
        }
        
        const { data: insertData, error: insertError } = await supabase
          .from('profiles')
          .insert([testProfile])
          .select()
          .single()
        
        if (insertError) {
          addResult('Database Operations Test', 'error', `Insert failed: ${insertError.message}`)
        } else {
          addResult('Database Operations Test', 'success', 'Insert operation successful')
          
          // Clean up test data
          await supabase
            .from('profiles')
            .delete()
            .eq('id', insertData.id)
          
          addResult('Database Operations Test', 'success', 'Test data cleaned up successfully')
        }
      } catch (err) {
        addResult('Database Operations Test', 'error', `Error: ${err.message}`)
      }

      addResult('Schema Fixes Complete', 'success', 'All schema fixes have been attempted')

    } catch (error) {
      addResult('Schema Fixes Error', 'error', `Unexpected error: ${error.message}`)
    } finally {
      setFixing(false)
    }
  }

  const copySQL = () => {
    const sql = `-- Run this SQL in Supabase SQL Editor if automatic fixes fail
-- Go to: https://supabase.com/dashboard/project/iznvctyzvtloodzmsfhc/sql

-- Add emergency_contact column
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS emergency_contact JSONB;

-- Create patients view for compatibility
CREATE OR REPLACE VIEW patients AS
SELECT 
  id, firebase_uid, email, full_name, phone, date_of_birth, gender, 
  address, emergency_contact, profile_picture, bio, is_active, 
  created_at, updated_at
FROM profiles 
WHERE role = 'patient';

-- Update sample data
UPDATE profiles 
SET emergency_contact = jsonb_build_object(
  'name', 'Emergency Contact',
  'relationship', 'Family',
  'phone', '******-0000',
  'email', '<EMAIL>'
)
WHERE role = 'patient' AND emergency_contact IS NULL;`

    navigator.clipboard.writeText(sql)
  }

  const getStatusIcon = (success) => {
    if (success === 'success') return <CheckCircle className="w-4 h-4 text-green-600" />
    if (success === 'error') return <XCircle className="w-4 h-4 text-red-600" />
    if (success === 'warning') return <AlertTriangle className="w-4 h-4 text-yellow-600" />
    return <Database className="w-4 h-4 text-blue-600" />
  }

  const getStatusColor = (success) => {
    if (success === 'success') return 'green'
    if (success === 'error') return 'red'
    if (success === 'warning') return 'yellow'
    return 'blue'
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Wrench className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Database Schema Fixer</h3>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={copySQL}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Copy className="w-4 h-4 mr-2" />
            Copy SQL
          </button>
          
          <button
            onClick={runSchemaFixes}
            disabled={fixing}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {fixing ? (
              <Loader className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Play className="w-4 h-4 mr-2" />
            )}
            {fixing ? 'Fixing...' : 'Fix Schema Issues'}
          </button>
        </div>
      </div>

      {/* Issues to Fix */}
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h4 className="font-medium text-yellow-900 mb-2">Issues This Will Fix:</h4>
        <ul className="list-disc list-inside text-sm text-yellow-800 space-y-1">
          <li>Missing 'emergency_contact' column in profiles table</li>
          <li>Missing 'patients' table/view for compatibility</li>
          <li>Foreign key constraint violations</li>
          <li>Sample data updates for testing</li>
        </ul>
      </div>

      {/* Fix Results */}
      {fixResults.length > 0 && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {fixResults.map((result, index) => (
            <div key={index} className={`p-3 rounded border bg-${getStatusColor(result.success)}-50 border-${getStatusColor(result.success)}-200`}>
              <div className="flex items-center space-x-2">
                {getStatusIcon(result.success)}
                <span className={`font-medium text-${getStatusColor(result.success)}-800`}>
                  {result.step}
                </span>
                <span className="text-xs text-gray-500">{result.timestamp}</span>
              </div>
              <p className={`text-sm mt-1 text-${getStatusColor(result.success)}-700`}>
                {result.message}
              </p>
              {result.details && (
                <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
                  <pre className="whitespace-pre-wrap">{JSON.stringify(result.details, null, 2)}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {fixResults.length === 0 && !fixing && (
        <div className="text-center py-8 text-gray-500">
          <Wrench className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>Click "Fix Schema Issues" to resolve database schema problems</p>
          <p className="text-sm mt-1">This will add missing columns, create compatibility views, and fix constraints</p>
        </div>
      )}
    </div>
  )
}

export default DatabaseSchemaFixer
